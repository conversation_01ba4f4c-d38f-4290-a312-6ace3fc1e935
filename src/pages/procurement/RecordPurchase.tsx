import React, { useState, useEffect } from 'react';
import { FileText, Search, Filter, Plus, Eye, Pencil, Trash2, Building, Settings } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { getPurchaseRecords, deletePurchaseRecord } from '../../lib/api';
import PurchaseRecordClosureModal from '../../components/modals/PurchaseRecordClosureModal';

interface PurchaseRecord {
  id: string;
  record_number: string;
  supplier: string;
  record_date: string;
  pricing_model: string;
  items_subtotal: number;
  additional_costs_total: number;
  total_amount: number;
  status: string;
  purchase_record_items: Array<{
    product_name: string;
    sku_code: string;
    quantity: number;
    unit_type: string;
  }>;
}

const RecordPurchase: React.FC = () => {
  const navigate = useNavigate();
  const [records, setRecords] = useState<PurchaseRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [closureModal, setClosureModal] = useState<{
    isOpen: boolean;
    recordId: string;
    currentStatus: string;
    recordNumber: string;
  }>({
    isOpen: false,
    recordId: '',
    currentStatus: '',
    recordNumber: ''
  });

  useEffect(() => {
    loadPurchaseRecords();
  }, []);

  const loadPurchaseRecords = async () => {
    try {
      const data = await getPurchaseRecords();
      setRecords(data || []);
    } catch (error) {
      console.error('Error loading purchase records:', error);
      toast.error('Failed to load purchase records');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRecord = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this purchase record?')) {
      try {
        await deletePurchaseRecord(id);
        setRecords(prev => prev.filter(record => record.id !== id));
        toast.success('Purchase record deleted successfully');
      } catch (error) {
        console.error('Error deleting purchase record:', error);
        toast.error('Failed to delete purchase record');
      }
    }
  };

  const handleOpenClosureModal = (record: PurchaseRecord) => {
    setClosureModal({
      isOpen: true,
      recordId: record.id,
      currentStatus: record.status,
      recordNumber: record.record_number
    });
  };

  const handleCloseClosureModal = () => {
    setClosureModal({
      isOpen: false,
      recordId: '',
      currentStatus: '',
      recordNumber: ''
    });
  };

  const handleStatusUpdated = () => {
    loadPurchaseRecords();
  };

  const filteredRecords = records.filter(record => {
    const matchesSearch = 
      record.supplier.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.record_number.toLowerCase().includes(searchTerm.toLowerCase());
      
    const matchesStatus = selectedStatus === 'all' || record.status === selectedStatus;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'partial_closure':
        return 'bg-yellow-100 text-yellow-800';
      case 'full_closure':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading purchase records...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <FileText className="h-6 w-6 text-green-600 mr-2" />
          <h1 className="text-2xl font-bold text-gray-800">Purchase Records</h1>
        </div>
        <button 
          onClick={() => navigate('/record-purchase/new')}
          className="bg-green-600 text-white rounded-md px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center"
        >
          <Plus className="h-4 w-4 mr-1" />
          New Purchase Record
        </button>
      </div>

      {/* Purchase Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Purchases</p>
              <p className="text-2xl font-bold text-gray-800">
                ₹{records.reduce((sum, record) => sum + record.total_amount, 0).toLocaleString()}
              </p>
            </div>
            <div className="h-10 w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
              <FileText className="h-5 w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Records</p>
              <p className="text-2xl font-bold text-gray-800">{records.length}</p>
            </div>
            <div className="h-10 w-10 flex items-center justify-center rounded-full bg-blue-100 text-blue-600">
              <FileText className="h-5 w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Partial Closure</p>
              <p className="text-2xl font-bold text-gray-800">
                {records.filter(record => record.status === 'partial_closure').length}
              </p>
            </div>
            <div className="h-10 w-10 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600">
              <FileText className="h-5 w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Full Closure</p>
              <p className="text-2xl font-bold text-gray-800">
                {records.filter(record => record.status === 'full_closure').length}
              </p>
            </div>
            <div className="h-10 w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
              <FileText className="h-5 w-5" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-3 md:space-y-0">
        <div className="relative flex-1 max-w-xs">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500"
            placeholder="Search records..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="flex items-center">
            <Filter className="h-4 w-4 text-gray-500 mr-2" />
            <span className="text-sm text-gray-500">Status:</span>
          </div>
          <select
            className="border border-gray-300 rounded-md text-sm py-2 px-3 bg-white focus:outline-none focus:ring-green-500 focus:border-green-500"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="all">All</option>
            <option value="partial_closure">Partial Closure</option>
            <option value="full_closure">Full Closure</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      {/* Purchase Records Table */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Record Details
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Supplier
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Items
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Amount
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredRecords.map((record) => (
                <tr key={record.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                        <FileText className="h-5 w-5" />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{record.record_number}</div>
                        <div className="text-sm text-gray-500">{formatDateTime(record.record_date)}</div>
                        <div className="text-sm text-gray-500 capitalize">{record.pricing_model}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Building className="h-4 w-4 text-gray-400 mr-2" />
                      <div className="text-sm text-gray-900">{record.supplier}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {record.purchase_record_items.slice(0, 2).map((item, index) => (
                        <div key={index}>
                          {item.product_name} - {item.quantity} {item.unit_type === 'box' ? 'boxes' : 'kg'}
                        </div>
                      ))}
                      {record.purchase_record_items.length > 2 && (
                        <div className="text-sm text-gray-500">
                          +{record.purchase_record_items.length - 2} more items
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">₹{record.total_amount.toLocaleString()}</div>
                    <div className="text-sm text-gray-500">
                      Items: ₹{record.items_subtotal.toLocaleString()}
                      {record.additional_costs_total > 0 && (
                        <span> + ₹{record.additional_costs_total.toLocaleString()}</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(record.status)}`}>
                      {record.status === 'partial_closure' ? 'Partial Closure' : 
                       record.status === 'full_closure' ? 'Full Closure' : 
                       record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => navigate(`/record-purchase/view/${record.id}`)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      {record.status === 'partial_closure' && (
                        <button 
                          onClick={() => navigate(`/record-purchase/edit/${record.id}`)}
                          className="text-gray-600 hover:text-gray-900"
                          title="Edit Record"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                      )}
                      {record.status !== 'cancelled' && (
                        <button 
                          onClick={() => handleOpenClosureModal(record)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Manage Closure Status"
                        >
                          <Settings className="h-4 w-4" />
                        </button>
                      )}
                      {record.status === 'partial_closure' && (
                        <button 
                          onClick={() => handleDeleteRecord(record.id)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete Record"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredRecords.length === 0 && !loading && (
          <div className="py-12 text-center text-gray-500">
            <FileText className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Purchase Records Found</h3>
            <p className="text-sm text-gray-500 mb-4">
              {records.length === 0 
                ? "Get started by creating your first purchase record."
                : "No records match your current search and filter criteria."
              }
            </p>
            {records.length === 0 && (
              <button
                onClick={() => navigate('/record-purchase/new')}
                className="bg-green-600 text-white rounded-md px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200"
              >
                Create First Purchase Record
              </button>
            )}
          </div>
        )}
      </div>

      {/* Closure Modal */}
      <PurchaseRecordClosureModal
        isOpen={closureModal.isOpen}
        onClose={handleCloseClosureModal}
        recordId={closureModal.recordId}
        currentStatus={closureModal.currentStatus}
        recordNumber={closureModal.recordNumber}
        onStatusUpdated={handleStatusUpdated}
      />
    </div>
  );
};

export default RecordPurchase;
